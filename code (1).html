<!DOCTYPE html>
<html lang="cs">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Členské příspěvky 2024 - Box Club Veselí nad Lužnicí</title>
    <style>
      /* --- Import písem --- */
      @import url("https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto:wght@400;700;900&family=Anton&display=swap");

      /* --- Obecné styly pro obrazovku --- */
      body {
        font-family: "Roboto", sans-serif;
        background-color: #0a0a0a;
        background-image: linear-gradient(
            45deg,
            rgba(217, 35, 35, 0.1) 25%,
            transparent 25%
          ),
          linear-gradient(-45deg, rgba(217, 35, 35, 0.1) 25%, transparent 25%),
          linear-gradient(45deg, transparent 75%, rgba(217, 35, 35, 0.1) 75%),
          linear-gradient(-45deg, transparent 75%, rgba(217, 35, 35, 0.1) 75%);
        background-size: 30px 30px;
        background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
        color: #333;
        line-height: 1.5;
        margin: 0;
        padding: 15px;
      }

      .container {
        max-width: 210mm;
        margin: 0 auto;
        padding: 25mm;
        background-color: #fff;
        border: 8px solid #d92323;
        border-top: 15px solid #d92323;
        border-bottom: 15px solid #d92323;
        box-shadow: inset 0 0 0 3px #000, 0 0 30px rgba(0, 0, 0, 0.8);
        position: relative;
      }

      .container::before {
        content: "";
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        border: 3px solid #000;
        pointer-events: none;
      }

      header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 5px double #d92323;
        padding-bottom: 20px;
        position: relative;
      }

      header::after {
        content: "🥊 🥊 🥊";
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        padding: 0 15px;
        font-size: 1.2em;
      }

      .logo {
        max-width: 100px;
        margin-bottom: 15px;
        filter: drop-shadow(3px 3px 6px rgba(0, 0, 0, 0.3));
        border: 3px solid #d92323;
        border-radius: 50%;
        padding: 10px;
        background: #fff;
      }

      h1 {
        font-family: "Anton", sans-serif;
        color: #d92323;
        font-size: 3.8em;
        text-transform: uppercase;
        margin: 0;
        letter-spacing: 4px;
        line-height: 0.9;
        text-shadow: 3px 3px 0px #000, -1px -1px 0px #000, 1px -1px 0px #000,
          -1px 1px 0px #000, 1px 1px 0px #000;
        transform: perspective(500px) rotateX(15deg);
      }

      h2 {
        font-family: "Bebas Neue", sans-serif;
        font-size: 1.6em;
        color: #000;
        font-weight: 400;
        margin-top: 10px;
        letter-spacing: 3px;
        text-transform: uppercase;
        border: 2px solid #000;
        padding: 8px 20px;
        display: inline-block;
        background: linear-gradient(45deg, #fff 0%, #f0f0f0 100%);
      }

      .pricing-section {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
        flex-wrap: wrap;
        justify-content: center;
      }

      .price-card {
        flex: 1 1 240px;
        background: linear-gradient(135deg, #000 0%, #1a1a1a 50%, #000 100%);
        color: #fff;
        border: 4px solid #d92323;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        transition: transform 0.2s, box-shadow 0.2s;
        display: flex;
        flex-direction: column;
        position: relative;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      .price-card::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #d92323, #ff4444, #d92323, #aa1111);
        border-radius: 12px;
        z-index: -1;
      }

      .price-card:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 30px rgba(217, 35, 35, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .price-card h3 {
        font-family: "Anton", sans-serif;
        font-size: 2.2em;
        margin-top: 0;
        color: #fff;
        letter-spacing: 2px;
        text-transform: uppercase;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        border-bottom: 3px solid #d92323;
        padding-bottom: 10px;
        margin-bottom: 15px;
      }

      .price-card ul {
        list-style: none;
        padding: 0;
        margin: 0;
        flex-grow: 1;
      }

      .price-card li {
        font-size: 1.1em;
        font-weight: 700;
        padding: 12px 0;
        border-bottom: 2px dotted #d92323;
        position: relative;
      }

      .price-card li:last-child {
        border-bottom: none;
      }

      .price-card li::before {
        content: "▶";
        color: #d92323;
        font-weight: bold;
        position: absolute;
        left: -15px;
      }

      /* Zlatá barva pro ceny */
      .price-card li span {
        font-family: "Anton", sans-serif;
        font-size: 1.8em;
        color: #ffd700;
        letter-spacing: 1px;
        display: block;
        margin-top: 8px;
        text-shadow: 2px 2px 0px #000, -1px -1px 0px #000, 1px -1px 0px #000,
          -1px 1px 0px #000;
        transform: scale(1.1);
      }

      .price-card li small {
        font-family: "Roboto", sans-serif;
        font-weight: 400;
        font-size: 0.75em;
        color: #ccc;
        display: block;
        font-style: italic;
        text-transform: none;
        margin-top: 5px;
      }

      .info-section h4 {
        font-family: "Anton", sans-serif;
        font-size: 1.8em;
        color: #000;
        border: 3px solid #d92323;
        background: linear-gradient(45deg, #d92323, #ff4444);
        color: #fff;
        padding: 12px 20px;
        margin-top: 25px;
        margin-bottom: 15px;
        letter-spacing: 2px;
        display: flex;
        align-items: center;
        text-transform: uppercase;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      /* Ikona rukavice */
      .icon-glove {
        width: 28px;
        height: 28px;
        margin-right: 12px;
        fill: #fff;
        filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.5));
      }

      .info-section p {
        margin-bottom: 12px;
        font-size: 1.05em;
        font-weight: 500;
        line-height: 1.6;
      }

      .placeholder {
        border: 3px solid #d92323;
        border-left: 8px solid #d92323;
        padding: 18px;
        margin: 15px 0;
        background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
        font-weight: 600;
        border-radius: 5px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .footer-note {
        margin-top: 30px;
        padding: 20px;
        border: 3px double #d92323;
        text-align: center;
        font-size: 0.95em;
        color: #000;
        font-weight: 600;
        background: linear-gradient(135deg, #fff 0%, #f5f5f5 100%);
        border-radius: 5px;
        position: relative;
      }

      .footer-note::before {
        content: "⚠️";
        font-size: 1.2em;
        margin-right: 8px;
      }

      /* --- Styly pro TISK na A4 --- */
      @media print {
        * {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        body {
          background: #fff !important;
          padding: 0 !important;
          font-size: 10pt !important;
          margin: 0 !important;
        }

        .container {
          box-shadow: none !important;
          margin: 0 !important;
          padding: 15mm !important;
          max-width: 100% !important;
          border: 6px solid #d92323 !important;
          border-top: 10px solid #d92323 !important;
          border-bottom: 10px solid #d92323 !important;
          background: #fff !important;
        }

        .container::before {
          border: 2px solid #000 !important;
        }

        header::after {
          color: #000 !important;
        }

        .logo {
          max-width: 80px !important;
          border: 2px solid #d92323 !important;
        }

        h1 {
          font-size: 2.8em !important;
          text-shadow: none !important;
          transform: none !important;
        }

        h2 {
          font-size: 1.3em !important;
          border: 1px solid #000 !important;
          padding: 5px 15px !important;
        }

        .pricing-section {
          gap: 15px !important;
          margin-bottom: 20px !important;
        }

        .price-card {
          background: #fff !important;
          color: #000 !important;
          border: 3px solid #d92323 !important;
          border-radius: 5px !important;
          box-shadow: none !important;
          padding: 15px !important;
        }

        .price-card::before {
          display: none !important;
        }

        .price-card h3 {
          color: #d92323 !important;
          font-size: 1.8em !important;
          text-shadow: none !important;
          border-bottom: 2px solid #d92323 !important;
        }

        .price-card li::before {
          color: #d92323 !important;
        }

        .price-card li span {
          color: #d92323 !important;
          text-shadow: none !important;
          transform: none !important;
          font-size: 1.5em !important;
        }

        .info-section h4 {
          background: #d92323 !important;
          color: #fff !important;
          font-size: 1.5em !important;
          text-shadow: none !important;
          box-shadow: none !important;
          padding: 8px 15px !important;
        }

        .icon-glove {
          fill: #fff !important;
          filter: none !important;
        }

        .placeholder {
          background: #f5f5f5 !important;
          border: 2px solid #d92323 !important;
          border-left: 5px solid #d92323 !important;
          box-shadow: none !important;
          padding: 12px !important;
        }

        .footer-note {
          border: 2px solid #d92323 !important;
          background: #f8f8f8 !important;
          box-shadow: none !important;
          padding: 15px !important;
        }

        /* Zajistit, že se vše vejde na jednu stránku */
        .container {
          page-break-inside: avoid !important;
        }

        .price-card {
          page-break-inside: avoid !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <header>
        <img
          src="logo.png"
          alt="Logo Box Club Veselí nad Lužnicí"
          class="logo"
        />
        <h1>Členské příspěvky</h1>
        <h2>Box Club Veselí nad Lužnicí</h2>
      </header>

      <section class="pricing-section">
        <div class="price-card">
          <h3>Mládež <small>(do 15 let)</small></h3>
          <ul>
            <li>Členství 12 měsíců<span>4 000 Kč</span></li>
            <li>Členství 6 měsíců<span>2 500 Kč</span></li>
            <li>Členství 1 měsíc<span>500 Kč</span></li>
          </ul>
        </div>
        <div class="price-card">
          <h3>Dospělí</h3>
          <ul>
            <li>Členství 12 měsíců<span>6 000 Kč</span></li>
            <li>Členství 6 měsíců<span>3 500 Kč</span></li>
            <li>Členství 1 měsíc<span>600 Kč</span></li>
          </ul>
        </div>
        <div class="price-card">
          <h3>Jednorázové vstupy</h3>
          <ul>
            <li>Trénink - Mládež<span>100 Kč</span></li>
            <li>Trénink - Dospělí<span>130 Kč</span></li>
            <li>
              Permanentka 10 vstupů<span>1 200 Kč</span
              ><small>1 vstup za 120 Kč</small>
            </li>
          </ul>
        </div>
      </section>

      <section class="info-section">
        <h4>
          <!-- SVG ikona boxerské rukavice -->
          <svg
            class="icon-glove"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path
              d="M19.1 8.29c-1.39-2.38-4.2-3.29-6.59-1.9L9.4 8.01C8.38 7.58 7.23 8.09 6.8 9.11L3.9 16.5c-.32.74.03 1.58.74 2.03l3.2 2.05c.48.31 1.08.33 1.58.06l1.2-.67c.39-.22.65-.62.65-1.06v-1.28l1.41-1.41c.31-.32.29-.83-.04-1.12l-1.04-1.04h.01c.42 0 .82-.17 1.12-.47l3.23-3.23c.63-.63.63-1.65 0-2.28L19.1 8.29zM8.51 17.5l-2.83-1.81 2.5-6.12 1.53.64-1.2 7.29zm8.55-8.55c-.23.23-.62.23-.85 0l-3.23-3.23c1.23-.74 2.89-.48 3.86.85.98 1.33.7 3-1.06 4.23l1.28-1.85z"
            />
          </svg>
          Platební podmínky
        </h4>
        <p>
          <strong>Stávající členové:</strong> Platba na kalendářní rok je
          jednorázová, splatná nejpozději do konce ledna 2024.
        </p>
        <p>
          <strong>Noví členové:</strong> Platba poměrné části za zbývající
          měsíce do konce kalendářního roku.
        </p>

        <div class="placeholder">
          <strong>Platba na účet:</strong> [Doplnit číslo účtu, např.
          123456789/0800]<br />
          <strong>Variabilní symbol:</strong> [Doplnit, např. rodné číslo bez
          lomítka]<br />
          <strong>Zpráva pro příjemce:</strong> Jméno a příjmení člena
        </div>

        <h4>
          <svg
            class="icon-glove"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path
              d="M19.1 8.29c-1.39-2.38-4.2-3.29-6.59-1.9L9.4 8.01C8.38 7.58 7.23 8.09 6.8 9.11L3.9 16.5c-.32.74.03 1.58.74 2.03l3.2 2.05c.48.31 1.08.33 1.58.06l1.2-.67c.39-.22.65-.62.65-1.06v-1.28l1.41-1.41c.31-.32.29-.83-.04-1.12l-1.04-1.04h.01c.42 0 .82-.17 1.12-.47l3.23-3.23c.63-.63.63-1.65 0-2.28L19.1 8.29zM8.51 17.5l-2.83-1.81 2.5-6.12 1.53.64-1.2 7.29zm8.55-8.55c-.23.23-.62.23-.85 0l-3.23-3.23c1.23-.74 2.89-.48 3.86.85.98 1.33.7 3-1.06 4.23l1.28-1.85z"
            />
          </svg>
          Tréninky
        </h4>
        <div class="placeholder">
          <strong>Mládež:</strong> [Doplnit dny a časy, např. Úterý a Čtvrtek,
          16:30 - 18:00]<br />
          <strong>Dospělí:</strong> [Doplnit dny a časy, např. Pondělí, Středa,
          Pátek, 18:00 - 19:30]
        </div>

        <h4>
          <svg
            class="icon-glove"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path
              d="M19.1 8.29c-1.39-2.38-4.2-3.29-6.59-1.9L9.4 8.01C8.38 7.58 7.23 8.09 6.8 9.11L3.9 16.5c-.32.74.03 1.58.74 2.03l3.2 2.05c.48.31 1.08.33 1.58.06l1.2-.67c.39-.22.65-.62.65-1.06v-1.28l1.41-1.41c.31-.32.29-.83-.04-1.12l-1.04-1.04h.01c.42 0 .82-.17 1.12-.47l3.23-3.23c.63-.63.63-1.65 0-2.28L19.1 8.29zM8.51 17.5l-2.83-1.81 2.5-6.12 1.53.64-1.2 7.29zm8.55-8.55c-.23.23-.62.23-.85 0l-3.23-3.23c1.23-.74 2.89-.48 3.86.85.98 1.33.7 3-1.06 4.23l1.28-1.85z"
            />
          </svg>
          Kontakt & Info
        </h4>
        <div class="placeholder">
          <strong>Trenér / Kontaktní osoba:</strong> [Doplnit jméno a
          telefon]<br />
          <strong>Adresa:</strong> [Doplnit adresu tělocvičny]<br />
          <strong>Web / Facebook:</strong> [Doplnit odkaz]
        </div>
      </section>

      <footer class="footer-note">
        <p>
          Zaplacené členské příspěvky jsou nevratné, a to i v případě
          předčasného ukončení docházky.
        </p>
      </footer>
    </div>
  </body>
</html>
